import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { jwtDecode } from 'jwt-decode'

interface User {
  id: string
  name: string
  email: string
  picture: string
  credits?: number;
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const token = ref<string | null>(null);

  function initializeAuth() {
    const storedToken = localStorage.getItem('authToken');
    const storedUser = localStorage.getItem('authUser');
    if (storedToken && storedUser) {
      try {
        const parsedUser: User = JSON.parse(storedUser);
        token.value = storedToken;
        user.value = parsedUser;
        isAuthenticated.value = true;
        console.log("Auth initialized from localStorage");
      } catch (error) {
        console.error("Failed to parse user data from localStorage", error);
        localStorage.removeItem('authToken');
        localStorage.removeItem('authUser');
      }
    } else {
        console.log("No auth data found in localStorage");
    }
  }

  function handleCredentialResponse(response: CredentialResponse) {
    const credential = response.credential
    if (!credential) return

    try {
      const decoded = jwtDecode<{
        sub: string
        name: string
        email: string
        picture: string
      }>(credential)

      user.value = {
        id: decoded.sub,
        name: decoded.name,
        email: decoded.email,
        picture: decoded.picture,
      }
      isAuthenticated.value = true
      token.value = credential;

      localStorage.setItem('authToken', token.value);
      localStorage.setItem('authUser', JSON.stringify(user.value));
      console.log("User logged in and state persisted");

    } catch (error) {
      console.error('Error decoding JWT:', error)
      logout();
    }
  }

  function logout() {
    console.log("Logging out user");
    user.value = null
    isAuthenticated.value = false
    token.value = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('authUser');
    google.accounts.id.disableAutoSelect()
  }

  return { user, isAuthenticated, token, initializeAuth, handleCredentialResponse, logout }
})
