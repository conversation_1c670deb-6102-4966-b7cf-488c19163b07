/// <reference types="vite/client" />
/// <reference types="vite-plugin-pages/client" />

// Google Identity Services types (basic)
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: {
            client_id: string
            callback: (response: CredentialResponse) => void
            auto_select?: boolean
            cancel_on_tap_outside?: boolean
          }) => void
          renderButton: (element: HTMLElement, options: {
            theme?: string
            size?: string
            type?: string
          }) => void
          disableAutoSelect: () => void
          prompt: (momentNotification?: (notification: any) => void) => void
          revoke: (email: string, done: () => void) => void
        }
      }
    }
  }

  interface CredentialResponse {
    credential: string;
    select_by?: 'auto'|'user'|'user_1tap'|'user_2tap'|'btn'|'btn_confirm'|'btn_add_session'|'btn_confirm_add_session';
    clientId?: string;
  }
}

interface ImportMetaEnv {
  readonly VITE_GOOGLE_CLIENT_ID: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

export {};
