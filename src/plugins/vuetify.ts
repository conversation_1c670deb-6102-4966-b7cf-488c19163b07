import "vuetify/styles";
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import { aliases, mdi } from "vuetify/iconsets/mdi";
import "@mdi/font/css/materialdesignicons.css";

const myCustomDarkTheme = {
  dark: true,
  colors: {
    background: "#000000",
    surface: "#1A1A1A",
    primary: "#E4B869",
    secondary: "#8A8D93",
    error: "#CF6679",
    info: "#2196F3",
    success: "#4CAF50",
    warning: "#FB8C00",
    "on-background": "#E0E0E0",
    "on-surface": "#E0E0E0",
    "on-primary": "#121212",
    "on-secondary": "#FFFFFF",
    "on-error": "#000000",
  },
};

export default createVuetify({
  components,
  directives,
  theme: {
    defaultTheme: "myCustomDarkTheme",
    themes: {
      myCustomDarkTheme,
    },
  },
  icons: {
    defaultSet: "mdi",
    aliases,
    sets: {
      mdi,
    },
  },
});
