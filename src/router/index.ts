import { createRouter, createWebHistory } from 'vue-router'
import Home from '../pages/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../pages/About.vue')
    },
    {
      path: '/generate',
      name: 'generate',
      component: () => import('../pages/Generate.vue')
    },
    {
      path: '/history',
      name: 'history',
      component: () => import('../pages/History.vue')
    },
    {
      path: '/pricing',
      name: 'pricing', 
      component: () => import('../pages/Pricing.vue')
    }
  ]
})

export default router
