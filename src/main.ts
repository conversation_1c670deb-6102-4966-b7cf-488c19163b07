import { createPinia } from 'pinia'
import './style.css'
import App from './App.vue'
import vuetify from './plugins/vuetify'
import { ViteSSG } from 'vite-ssg'
import { useAuthStore } from './store/auth'
import routes from '~pages'

export const createApp = ViteSSG(
  App,
  { routes, base: import.meta.env.BASE_URL },
  ({ app, router, isClient, initialState }) => {
    const pinia = createPinia()
    app.use(pinia)
    if (import.meta.env.SSR) {
      initialState.pinia = pinia.state.value
    } else if (isClient) {
      pinia.state.value = initialState?.pinia || {}
    }
    app.use(vuetify)
    if (isClient) {
      const authStore = useAuthStore(pinia)
      authStore.initializeAuth()
    }
  }
)
