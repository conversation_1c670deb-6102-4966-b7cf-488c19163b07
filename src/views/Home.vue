<script setup lang="ts">
import { useRouter } from "vue-router";
import { useAuthStore } from "../store/auth";

const router = useRouter();
const authStore = useAuthStore();

function navigateToGenerate() {
  router.push("/generate");
}
</script>

<template>
  <div class="home-page">
    <div class="hero-section">
      <div class="hero-content">
        <h1>AI Video Generation Platform</h1>
        <p class="subtitle">
          Transform your ideas into high-quality videos in seconds with
          cutting-edge AI technology
        </p>
        <div class="cta-buttons">
          <button class="cta-primary" @click="navigateToGenerate">
            Start Creating Now
          </button>
          <button class="cta-secondary" @click="router.push('/pricing')">
            View Pricing Plans
          </button>
        </div>
      </div>
      <div class="hero-image">
        <video
          src="/hero.mp4"
          autoplay
          loop
          muted
          playsinline
          alt="AI Video Generation Showcase"
        ></video>
      </div>
    </div>

    <div class="features-section">
      <h2>Powerful Features, Endless Possibilities</h2>

      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"
              ></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <path d="M8 13h8"></path>
              <path d="M8 17h8"></path>
              <path d="M8 9h1"></path>
            </svg>
          </div>
          <h3>Easy to Use</h3>
          <p>
            Intuitive interface design, just enter prompts to generate
            professional-grade video content
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="2" y1="12" x2="22" y2="12"></line>
              <path
                d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
              ></path>
            </svg>
          </div>
          <h3>Efficient Generation</h3>
          <p>
            Advanced AI algorithms complete video generation in seconds, saving
            your valuable time
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <circle cx="8.5" cy="8.5" r="1.5"></circle>
              <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
          </div>
          <h3>High-Quality Output</h3>
          <p>
            Generate professional-level video content for various needs like
            social media, marketing and education
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
          </div>
          <h3>Secure & Reliable</h3>
          <p>
            Protect your creativity and data security, all generated content
            belongs to you with no copyright issues
          </p>
        </div>
      </div>
    </div>

    <div class="testimonials-section">
      <h2>Testimonials</h2>
      <div class="testimonials-container">
        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>
              "这款AI视频生成工具彻底改变了我的工作流程，现在我可以在几分钟内完成过去需要几小时的工作。"
            </p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">M</div>
            <div class="author-info">
              <h4>Ma Xiaoming</h4>
              <p>Content Creator</p>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>
              "界面简洁易用，生成的视频质量超出了我的预期。强烈推荐给所有需要快速制作视频内容的人。"
            </p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">L</div>
            <div class="author-info">
              <h4>Li Minghua</h4>
              <p>Marketing Manager</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="cta-section">
      <div class="cta-content">
        <h2>Start Your Creative Journey</h2>
        <p>
          Sign up now to experience the powerful AI video generation for free
        </p>
        <button class="cta-primary" @click="navigateToGenerate">
          Try for Free
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: var(--text-color, #e0e0e0);
  padding-top: 5rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  overflow: hidden;
}

.home-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(228, 184, 105, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(228, 184, 105, 0.03) 0%,
      transparent 50%
    );
  z-index: 0;
}

.home-page::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.02) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}

.hero-section {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
  padding: 5rem 2rem;
  min-height: 80vh;
}

.hero-content {
  max-width: 600px;
}

h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.subtitle {
  font-size: 1.5rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2.5rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.cta-primary {
  background: linear-gradient(135deg, #e4b869, #d4a040);
  color: #121212;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-primary:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(228, 184, 105, 0.4);
}

.cta-secondary {
  background-color: transparent;
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.2));
  color: var(--text-color, #e0e0e0);
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  border-color: rgba(228, 184, 105, 0.5);
  color: #e4b869;
  transform: translateY(-2px);
}

.hero-image {
  flex: 0 0 45%;
  position: relative;
}

.hero-image {
  flex: 0 0 45%;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.hero-image video {
  width: 100%;
  height: auto;
  display: block;
}

.features-section {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 5rem 2rem;
  text-align: center;
}

.features-section h2 {
  font-size: 2.5rem;
  margin-bottom: 3rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature-card {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border-color: rgba(228, 184, 105, 0.3);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(228, 184, 105, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: #e4b869;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #e4b869;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

.testimonials-section {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 5rem 2rem;
  text-align: center;
}

.testimonials-section h2 {
  font-size: 2.5rem;
  margin-bottom: 3rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.testimonials-container {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.testimonial-card {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  text-align: left;
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(228, 184, 105, 0.3);
}

.testimonial-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #e4b869;
  color: #121212;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin-right: 1rem;
}

.author-info h4 {
  margin: 0;
  color: #ffffff;
  font-size: 1.1rem;
}

.author-info p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.cta-section {
  position: relative;
  z-index: 1;
  background-color: rgba(30, 30, 35, 0.7);
  border-top: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  padding: 5rem 2rem;
  margin-top: 3rem;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.cta-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cta-section p {
  font-size: 1.2rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
}

@media (max-width: 992px) {
  .hero-section {
    flex-direction: column;
    text-align: center;
    padding: 3rem 1.5rem;
  }

  .hero-content {
    max-width: 100%;
    margin-bottom: 3rem;
  }

  h1 {
    font-size: 2.8rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .cta-buttons {
    justify-content: center;
  }

  .hero-image {
    flex: 0 0 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .testimonials-container {
    flex-direction: column;
    align-items: center;
  }

  .testimonial-card {
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .features-section h2,
  .testimonials-section h2,
  .cta-section h2 {
    font-size: 2rem;
  }
}
</style>
