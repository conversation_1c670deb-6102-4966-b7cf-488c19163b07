<script setup lang="ts">
import { ref } from "vue";
declare const URL: typeof window.URL;

// Controls current mode: 'text' (text-to-video) or 'image' (image-to-video)
const activeTab = ref("text");

// Controls audio generation toggle state
const generateAudio = ref(true);

// Prompt for video description
const prompt = ref("");

// Simulated user credits
const userCredits = ref(0);
const generationCost = 2000;

// Whether generation is in progress
const isGenerating = ref(false);

// Handles image upload
const uploadedImage = ref<File | null>(null);

function onFileChange(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    uploadedImage.value = target.files[0];
  }
}

function handleDrop(event: DragEvent) {
  if (event.dataTransfer && event.dataTransfer.files[0]) {
    uploadedImage.value = event.dataTransfer.files[0];
  }
}

// Simulates video generation
async function generate() {
  isGenerating.value = true;
  // Add API call logic here
  console.log("Generating video with prompt:", prompt.value);
  console.log("Mode:", activeTab.value);
  if (activeTab.value === "image") {
    console.log("Image:", uploadedImage.value?.name);
  }

  await new Promise((resolve) => setTimeout(resolve, 3000)); // Simulate network delay
  isGenerating.value = false;
}

// Resets the form
function resetForm() {
  prompt.value = "";
  uploadedImage.value = null;
  generateAudio.value = true;
}
</script>

<template>
  <div class="generate-page">
    <div class="generate-container">
      <div class="input-section">
        <div class="tabs">
          <button
            :class="{ active: activeTab === 'text' }"
            @click="activeTab = 'text'"
          >
            Text to Video
          </button>
          <button
            :class="{ active: activeTab === 'image' }"
            @click="activeTab = 'image'"
          >
            Image to Video
          </button>
        </div>

        <div class="controls">
          <p class="video-duration">Video duration: 8s</p>

          <div v-if="activeTab === 'text'" class="input-area">
            <label>Prompt</label>
            <textarea
              v-model="prompt"
              placeholder="Describe the video you want to generate..."
              rows="8"
            ></textarea>
            <p class="input-hint">
              Provide detailed and specific descriptions of what you want to see
              in the video.
            </p>
          </div>

          <div v-if="activeTab === 'image'" class="input-area">
            <label>Upload Image</label>
            <div
              class="dropzone"
              @dragover.prevent
              @dragenter.prevent
              @drop.prevent="handleDrop"
              @click="($refs.fileInput as HTMLInputElement).click()"
            >
              <template v-if="!uploadedImage">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="48"
                  height="48"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="upload-icon"
                >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                  <polyline points="17 8 12 3 7 8" />
                  <line x1="12" x2="12" y1="3" y2="15" />
                </svg>
                <span>Click or drop image here</span>
              </template>
              <div v-else class="image-preview">
                <img
                  :src="URL.createObjectURL(uploadedImage)"
                  alt="Uploaded preview"
                />
                <span>{{ uploadedImage.name }}</span>
              </div>
            </div>
            <input
              type="file"
              ref="fileInput"
              @change="onFileChange"
              accept="image/*"
              style="display: none"
            />

            <label class="prompt-label-image">Prompt</label>
            <textarea
              v-model="prompt"
              placeholder="Add any specific video prompts..."
              rows="3"
            ></textarea>
            <p class="input-hint">
              Provide detailed and specific descriptions of what you want to see
              in the video.
            </p>
          </div>

          <div class="audio-toggle-section">
            <div class="audio-label">
              <h4>Generate Audio</h4>
              <p>Generate natural and suitable audio for the output video.</p>
            </div>
            <label class="switch">
              <input type="checkbox" v-model="generateAudio" />
              <span class="slider round"></span>
            </label>
          </div>
        </div>

        <div class="action-bar">
          <button class="reset-btn" @click="resetForm" :disabled="isGenerating">
            Reset
          </button>
          <div class="generate-group">
            <div class="credit-info">
              <span>Credits: {{ userCredits }} remaining</span>
              <span
                >This generation will cost: {{ generationCost }} credits</span
              >
            </div>
            <button
              class="generate-btn"
              @click="generate"
              :disabled="isGenerating"
            >
              <span v-if="isGenerating" class="spinner"></span>
              {{ isGenerating ? "Generating..." : "Generate Video" }}
            </button>
          </div>
        </div>
      </div>

      <div class="preview-section">
        <h3>Preview</h3>
        <div class="preview-box">
          <div class="preview-placeholder">
            <span class="preview-spinner"></span>
            <p>Ready to create your video</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Main Layout & Theme */
.generate-page {
  background-color: #121212;
  color: #e0e0e0;
  padding: 6rem 2rem 2rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
}

.generate-container {
  display: grid;
  grid-template-columns: minmax(500px, 1.2fr) 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Left Column: Input Section */
.input-section {
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  border: 1px solid #282828;
  border-radius: 12px;
}

.controls {
  padding: 1.5rem;
  flex-grow: 1;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid #282828;
}

.tabs button {
  flex: 1;
  padding: 1rem;
  background-color: transparent;
  border: none;
  color: #888;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.tabs button.active {
  color: #e0e0e0;
  background-color: #202020;
  border-bottom-color: #e4b869;
}

/* Input Areas */
.video-duration {
  color: #aaa;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.input-area {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: #ccc;
  font-weight: 500;
}

.prompt-label-image {
  margin-top: 1.5rem;
}

textarea {
  width: 100%;
  padding: 1rem;
  background-color: #101010;
  border: 1px solid #333;
  border-radius: 8px;
  color: #e0e0e0;
  font-size: 1rem;
  resize: vertical;
  transition: all 0.3s ease;
}

textarea:focus {
  outline: none;
  border-color: #e4b869;
}

.input-hint {
  font-size: 0.8rem;
  color: #777;
  margin-top: 0.5rem;
}

/* Dropzone */
.dropzone {
  border: 2px dashed #333;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
  min-height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #888;
}
.dropzone:hover {
  border-color: #e4b869;
}
.upload-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 0.5rem;
}
.image-preview img {
  max-height: 100px;
  max-width: 100%;
  margin-bottom: 0.5rem;
  border-radius: 4px;
}
.image-preview span {
  font-size: 0.9rem;
  color: #ccc;
}

/* Audio Toggle */
.audio-toggle-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}
.audio-label h4 {
  margin: 0 0 0.25rem 0;
  font-weight: 500;
  color: #e0e0e0;
}
.audio-label p {
  margin: 0;
  font-size: 0.85rem;
  color: #888;
}
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #333;
  transition: 0.4s;
}
.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}
input:checked + .slider {
  background-color: #e4b869;
}
input:checked + .slider:before {
  transform: translateX(22px);
}
.slider.round {
  border-radius: 28px;
}
.slider.round:before {
  border-radius: 50%;
}

/* Action Bar */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid #282828;
  background-color: #1c1c1c;
  border-radius: 0 0 12px 12px;
}

.reset-btn {
  background-color: #282828;
  border: 1px solid #444;
  color: #ccc;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.reset-btn:hover:not(:disabled) {
  background-color: #333;
  border-color: #555;
}

.generate-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.credit-info {
  display: flex;
  flex-direction: column;
  text-align: right;
  font-size: 0.8rem;
  color: #888;
}
.credit-info span:first-child {
  color: #aaa;
}

.generate-btn {
  background-color: #e4b869;
  color: #121212;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.generate-btn:hover:not(:disabled) {
  filter: brightness(1.1);
}
.generate-btn:disabled,
.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  border-top-color: #121212;
  animation: spin 1s linear infinite;
}

/* Right Column: Preview Section */
.preview-section {
  background-color: #1a1a1a;
  border: 1px solid #282828;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}
.preview-section h3 {
  margin: 0 0 1rem 0;
  font-weight: 500;
  color: #ccc;
}
.preview-box {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #101010;
  border-radius: 8px;
}
.preview-placeholder {
  text-align: center;
  color: #888;
}
.preview-spinner {
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 4px solid #333;
  border-radius: 50%;
  border-top-color: #e4b869;
  animation: spin 1.5s linear infinite;
  margin-bottom: 1rem;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 1200px) {
  .generate-container {
    grid-template-columns: 1fr;
  }
}
</style>
