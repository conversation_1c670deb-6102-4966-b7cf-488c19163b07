<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

function navigateToGenerate() {
  router.push('/generate');
}
</script>

<template>
  <div class="history-page">
    <div class="history-container">
      <div class="empty-state">
        <div class="icon-container">
          <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="warning-icon">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        </div>
        <h2>No Videos Yet</h2>
        <p>You haven't generated any videos. Start creating your first masterpiece!</p>
        <button class="generate-btn" @click="navigateToGenerate">
          Generate Video
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.history-page {
  background-color: #121212;
  color: #e0e0e0;
  padding: 6rem 2rem 2rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
}

.history-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 10rem);
}

.empty-state {
  text-align: center;
  max-width: 500px;
}

.icon-container {
  margin-bottom: 1.5rem;
}

.warning-icon {
  color: #9e9e9e;
  opacity: 0.8;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #ffffff;
}

p {
  font-size: 1.2rem;
  color: #9e9e9e;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.generate-btn {
  background-color: #e4b869;
  color: #121212;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.generate-btn:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(228, 184, 105, 0.4);
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  border-top-color: #121212;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
