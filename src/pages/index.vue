<script setup lang="ts">
import { useRouter } from "vue-router";
import { useAuthStore } from "../store/auth";

const router = useRouter();
const authStore = useAuthStore();

function navigateToGenerate() {
  router.push("/generate");
}
</script>

<template>
  <div class="home-page">
    <div class="hero-section">
      <div class="hero-content">
        <h1>AI Video Generation Platform</h1>
        <p class="subtitle">
          Transform your ideas into high-quality videos in seconds with
          cutting-edge AI technology
        </p>
        <div class="cta-buttons">
          <button class="cta-primary" @click="navigateToGenerate">
            Start Creating Now
          </button>
          <button class="cta-secondary" @click="router.push('/pricing')">
            View Pricing Plans
          </button>
        </div>
      </div>
      <div class="hero-image">
        <video
          src="/hero.mp4"
          autoplay
          loop
          muted
          playsinline
          alt="AI Video Generation Showcase"
        ></video>
      </div>
    </div>

    <div class="showcases-section">
      <h2>探索使用Veo 3创建的视频</h2>
      <p class="section-subtitle">
        观看以与音频同步的示例AI生成视频，展示Veo 3的强大功能。
      </p>
      <div class="showcases-grid">
        <article class="tweet-container_article__RGxLz">
          <div class="tweet-header_header__ritpU">
            <a
              href="https://x.com/nmatares/status/1924931844879134804"
              class="tweet-header_avatar__1pZPy"
              target="_blank"
              rel="noopener noreferrer"
              ><div class="tweet-header_avatarOverflow__Mti2A">
                <img
                  alt="Nick Matarese"
                  width="48"
                  height="48"
                  src="https://pbs.twimg.com/profile_images/1487942563701018626/mqPvTuYI_normal.jpg"
                />
              </div>
              <div class="tweet-header_avatarOverflow__Mti2A">
                <div class="tweet-header_avatarShadow__Dm6qp"></div></div
            ></a>
            <div class="tweet-header_author__0djpf">
              <a
                href="https://x.com/nmatares/status/1924931844879134804"
                class="tweet-header_authorLink__9Kg2L"
                target="_blank"
                rel="noopener noreferrer"
                ><div class="tweet-header_authorLinkText__AkW3J">
                  <span title="Nick Matarese">Nick Matarese</span>
                </div></a
              >
              <div class="tweet-header_authorMeta__TqzyB">
                <a
                  href="https://x.com/nmatares/status/1924931844879134804"
                  class="tweet-header_username__l_Bh5"
                  target="_blank"
                  rel="noopener noreferrer"
                  ><span title="@nmatares">@nmatares</span></a
                >
                <div class="tweet-header_authorFollow__IxSf5">
                  <span class="tweet-header_separator__fMDhZ">·</span
                  ><a
                    href="https://x.com/intent/follow?screen_name=nmatares"
                    class="tweet-header_follow__qwW0Q"
                    target="_blank"
                    rel="noopener noreferrer"
                    >Follow</a
                  >
                </div>
              </div>
            </div>
            <a
              href="https://x.com/nmatares/status/1924931844879134804"
              class="tweet-header_brand__h4RBR"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="View on Twitter"
              ><svg
                viewBox="0 0 24 24"
                aria-hidden="true"
                class="tweet-header_twitterIcon__Sk7F3"
              >
                <g>
                  <path
                    d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
                  ></path>
                </g></svg
            ></a>
          </div>
          <p class="tweet-body_root__YdwCY" lang="en" dir="auto">
            <span
              >Let's check out that prompt adherence... "The camera follows a
              dachshund running through a living room and out of an open front
              door and onto a porch. It stands on the top stair overlooking the
              neighborhood as an ice cream truck drives by." - </span
            ><a
              href="https://x.com/hashtag/Veo3"
              class="tweet-link_root__eP4AF"
              target="_blank"
              rel="noopener noreferrer nofollow"
              >#Veo3</a
            >
          </p>
          <div class="tweet-media_root__a2E5X tweet-media_rounded__Emu5F">
            <div class="tweet-media_mediaWrapper__n_fwC">
              <div class="tweet-media_mediaContainer__EKTpm">
                <div
                  class="tweet-media_skeleton__tIOZS"
                  style="width: unset; padding-bottom: 56.25%"
                ></div>
                <video
                  class="tweet-media_image__ceoIl"
                  poster="https://pbs.twimg.com/amplify_video_thumb/1924931800700452864/img/dsOoBX7aJ9I_0VB_?format=jpg&amp;name=small"
                  playsinline=""
                  preload="none"
                  tabindex="0"
                  controls=""
                >
                  <source
                    src="https://video.twimg.com/amplify_video/1924931800700452864/vid/avc1/640x360/DtZE6aL8Ckb3FbdW.mp4?tag=14"
                    type="video/mp4"
                  /></video
                ><a
                  href="https://x.com/nmatares/status/1924931844879134804"
                  class="tweet-media-video_anchor__9qpuD tweet-media-video_viewReplies__5aC1c"
                  target="_blank"
                  rel="noopener noreferrer"
                  >View replies</a
                >
              </div>
            </div>
          </div>
          <div class="tweet-info_info__sUrmh">
            <a
              class="tweet-info-created-at_root__Fwx0p"
              href="https://x.com/nmatares/status/1924931844879134804"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="4:54 AM · May 21, 2025"
              ><time datetime="2025-05-20T20:54:49.000Z"
                >4:54 AM · May 21, 2025</time
              ></a
            ><a
              class="tweet-info_infoLink__NEj3j"
              href="https://help.x.com/en/x-for-websites-ads-info-and-privacy"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Twitter for Websites, Ads Information and Privacy"
              ><svg
                viewBox="0 0 24 24"
                aria-hidden="true"
                class="tweet-info_infoIcon__zbf_t"
              >
                <g>
                  <path
                    d="M13.5 8.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5S11.17 7 12 7s1.5.67 1.5 1.5zM13 17v-5h-2v5h2zm-1 5.25c5.66 0 10.25-4.59 10.25-10.25S17.66 1.75 12 1.75 1.75 6.34 1.75 12 6.34 22.25 12 22.25zM20.25 12c0 4.56-3.69 8.25-8.25 8.25S3.75 16.56 3.75 12 7.44 3.75 12 3.75s8.25 3.69 8.25 8.25z"
                  ></path>
                </g></svg
            ></a>
          </div>
          <div class="tweet-actions_actions__0vuqK">
            <a
              class="tweet-actions_like__t_LPy"
              href="https://x.com/intent/like?tweet_id=1924931844879134804"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Like. This Tweet has 1.3K likes"
              ><div class="tweet-actions_likeIconWrapper___z1QR">
                <svg
                  viewBox="0 0 24 24"
                  class="tweet-actions_likeIcon__Mk1h6"
                  aria-hidden="true"
                >
                  <g>
                    <path
                      d="M20.884 13.19c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z"
                    ></path>
                  </g>
                </svg>
              </div>
              <span class="tweet-actions_likeCount__0JFYD">1.3K</span></a
            ><a
              class="tweet-actions_reply__moeYd"
              href="https://x.com/intent/tweet?in_reply_to=1924931844879134804"
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Reply to this Tweet on Twitter"
              ><div class="tweet-actions_replyIconWrapper__YOoZS">
                <svg
                  viewBox="0 0 24 24"
                  class="tweet-actions_replyIcon__XHLqt"
                  aria-hidden="true"
                >
                  <g>
                    <path
                      d="M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z"
                    ></path>
                  </g>
                </svg>
              </div>
              <span class="tweet-actions_replyText__NE16h">Reply</span></a
            ><button
              type="button"
              class="tweet-actions_copy___hYiI"
              aria-label="Copy link"
            >
              <div class="tweet-actions_copyIconWrapper__H3l97">
                <svg
                  viewBox="0 0 24 24"
                  class="tweet-actions_copyIcon__LIRtP"
                  aria-hidden="true"
                >
                  <g>
                    <path
                      d="M18.36 5.64c-1.95-1.96-5.11-1.96-7.07 0L9.88 7.05 8.46 5.64l1.42-1.42c2.73-2.73 7.16-2.73 9.9 0 2.73 2.74 2.73 7.17 0 9.9l-1.42 1.42-1.41-1.42 1.41-1.41c1.96-1.96 1.96-5.12 0-7.07zm-2.12 3.53l-7.07 7.07-1.41-1.41 7.07-7.07 1.41 1.41zm-12.02.71l1.42-1.42 1.41 1.42-1.41 1.41c-1.96 1.96-1.96 5.12 0 7.07 1.95 1.96 5.11 1.96 7.07 0l1.41-1.41 1.42 1.41-1.42 1.42c-2.73 2.73-7.16 2.73-9.9 0-2.73-2.74-2.73-7.17 0-9.9z"
                    ></path>
                  </g>
                </svg>
              </div>
              <span class="tweet-actions_copyText__gcO0t">Copy link</span>
            </button>
          </div>
          <div class="tweet-replies_replies__7BiUx">
            <a
              class="tweet-replies_link__0RLuw"
              href="https://x.com/nmatares/status/1924931844879134804"
              target="_blank"
              rel="noopener noreferrer"
              ><span class="tweet-replies_text__dpQ3e">Read more on X</span></a
            >
          </div>
        </article>
      </div>
    </div>
    <div class="features-section">
      <h2>Powerful Features, Endless Possibilities</h2>

      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"
              ></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <path d="M8 13h8"></path>
              <path d="M8 17h8"></path>
              <path d="M8 9h1"></path>
            </svg>
          </div>
          <h3>Easy to Use</h3>
          <p>
            Intuitive interface design, just enter prompts to generate
            professional-grade video content
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="2" y1="12" x2="22" y2="12"></line>
              <path
                d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"
              ></path>
            </svg>
          </div>
          <h3>Efficient Generation</h3>
          <p>
            Advanced AI algorithms complete video generation in seconds, saving
            your valuable time
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <circle cx="8.5" cy="8.5" r="1.5"></circle>
              <polyline points="21 15 16 10 5 21"></polyline>
            </svg>
          </div>
          <h3>High-Quality Output</h3>
          <p>
            Generate professional-level video content for various needs like
            social media, marketing and education
          </p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
          </div>
          <h3>Secure & Reliable</h3>
          <p>
            Protect your creativity and data security, all generated content
            belongs to you with no copyright issues
          </p>
        </div>
      </div>
    </div>

    <div class="testimonials-section">
      <h2>Testimonials</h2>
      <div class="testimonials-container">
        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>
              "This AI video generation tool has completely transformed my
              workflow. What used to take me hours now gets done in minutes."
            </p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">M</div>
            <div class="author-info">
              <h4>Ma Xiaoming</h4>
              <p>Content Creator</p>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>
              "The interface is simple and intuitive, and the video quality
              exceeds expectations. Highly recommended for anyone needing to
              create video content quickly."
            </p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">L</div>
            <div class="author-info">
              <h4>Li Minghua</h4>
              <p>Marketing Manager</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="cta-section">
      <div class="cta-content">
        <h2>Start Your Creative Journey</h2>
        <p>
          Sign up now to experience the powerful AI video generation for free
        </p>
        <button class="cta-primary" @click="navigateToGenerate">
          Try for Free
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  background: linear-gradient(135deg, #13151a 0%, #171923 100%);
  position: relative;
  color: var(--text-color, #e0e0e0);
  padding-top: 5rem;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  overflow: hidden;
}

.home-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(228, 184, 105, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(228, 184, 105, 0.03) 0%,
      transparent 50%
    );
  z-index: 0;
}

.home-page::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.02) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
}

.hero-section {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
  padding: 5rem 2rem;
  min-height: 80vh;
}

.hero-content {
  max-width: 600px;
}

h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.subtitle {
  font-size: 1.5rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2.5rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.cta-primary {
  background: linear-gradient(135deg, #e4b869, #d4a040);
  color: #121212;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-primary:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(228, 184, 105, 0.4);
}

.cta-secondary {
  background-color: transparent;
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.2));
  color: var(--text-color, #e0e0e0);
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  border-color: rgba(228, 184, 105, 0.5);
  color: #e4b869;
  transform: translateY(-2px);
}

.hero-image {
  flex: 0 0 45%;
  position: relative;
}

.hero-image {
  flex: 0 0 45%;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.hero-image video {
  width: 100%;
  height: auto;
  display: block;
}

.features-section {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 5rem 2rem;
  text-align: center;
}

.features-section h2 {
  font-size: 2.5rem;
  margin-bottom: 3rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.feature-card {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border-color: rgba(228, 184, 105, 0.3);
}

.feature-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(228, 184, 105, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: #e4b869;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #e4b869;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

.testimonials-section {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 5rem 2rem;
  text-align: center;
}

.testimonials-section h2 {
  font-size: 2.5rem;
  margin-bottom: 3rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.testimonials-container {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.testimonial-card {
  background-color: rgba(30, 30, 35, 0.7);
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  text-align: left;
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(228, 184, 105, 0.3);
}

.showcases-section {
  position: relative;
  z-index: 1;
  max-width: 1400px;
  margin: 2rem auto 4rem;
  padding: 3rem 2rem 5rem;
  text-align: center;
  background-color: rgba(15, 15, 20, 0.4);
  border-radius: 16px;
}

.showcases-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.section-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto 2rem;
}

.showcases-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.tweet-container_article__RGxLz {
  background-color: rgba(30, 30, 35, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.12);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tweet-container_article__RGxLz:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-color: rgba(228, 184, 105, 0.3);
}

.tweet-header_header__ritpU {
  padding: 1rem 1rem 0.5rem;
}

.tweet-body_root__YdwCY {
  padding: 0 1rem;
}

.tweet-media_root__a2E5X {
  margin: 0.75rem 0;
}

.tweet-media_mediaContainer__EKTpm {
  border-radius: 12px;
  overflow: hidden;
}

.tweet-info_info__sUrmh {
  padding: 0.5rem 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.tweet-actions_actions__0vuqK {
  display: flex;
  padding: 0.5rem 1rem;
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .showcases-grid {
    max-width: 100%;
  }
}

.showcase-card {
  padding: 1.25rem;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(
    to bottom,
    rgba(25, 25, 30, 0.95),
    rgba(22, 22, 26, 0.95)
  );
  border: 1px solid rgba(255, 255, 255, 0.12);
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.showcase-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
  border-color: rgba(228, 184, 105, 0.3);
}

.showcase-card:nth-child(1):hover {
  border-color: rgba(228, 184, 105, 0.3);
}

.showcase-card:nth-child(2):hover {
  transform: translateY(-8px) rotate(0.5deg);
  border-color: rgba(29, 161, 242, 0.3);
}

.showcase-card:nth-child(3):hover {
  transform: translateY(-8px) rotate(-0.5deg);
  border-color: rgba(0, 186, 124, 0.3);
}

.showcase-card:nth-child(4):hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(228, 184, 105, 0.2);
  border-color: rgba(239, 243, 244, 0.15);
}

.showcase-card video {
  width: 100%;
  border-radius: 12px;
  background-color: #000;
  max-height: 300px;
  object-fit: cover;
  transition: transform 0.5s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.showcase-card:hover video {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.showcase-card video::-webkit-media-controls-panel {
  background-image: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.showcase-card video::-webkit-media-controls-play-button {
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
}

.showcase-card:nth-child(1) .showcase-footer a:first-child {
  color: #f91880;
}

.showcase-card:nth-child(2) .showcase-prompt a {
  color: #1d9bf0;
  position: relative;
  transition: color 0.2s ease;
}

.showcase-card:nth-child(2) .showcase-prompt a:hover {
  color: #1a8cd8;
  text-decoration: underline;
}

.showcase-card:nth-child(2) .showcase-footer a:nth-child(2)::before {
  content: "💬";
  color: #1d9bf0;
}

.showcase-card:nth-child(3) .showcase-prompt a {
  font-weight: 500;
  position: relative;
  transition: all 0.2s ease;
}

.showcase-card:nth-child(3) .showcase-prompt a:hover {
  background-color: rgba(29, 155, 240, 0.1);
  border-radius: 4px;
  padding: 0 4px;
  margin: 0 -4px;
}

.showcase-card:nth-child(3) .showcase-footer a:nth-child(3)::before {
  content: "🔄";
  color: #00ba7c;
}

.showcase-card:nth-child(4) .showcase-prompt::after {
  content: "👇";
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 1.2em;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.showcase-card:nth-child(4):hover .showcase-prompt::after {
  opacity: 1;
  transform: translateY(0);
}

.showcase-card:nth-child(4) .showcase-footer {
  position: relative;
  overflow: hidden;
}

.showcase-card:nth-child(4) .showcase-footer::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: -1px;
  width: 0;
  height: 1px;
  background: linear-gradient(to right, #e4b869, transparent);
  transition: width 0.6s ease;
}

.showcase-card:nth-child(4):hover .showcase-footer::before {
  width: 100%;
}

@media (max-width: 768px) {
  .showcases-grid {
    grid-template-columns: 1fr;
  }

  .showcase-card {
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
}

.testimonial-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #e4b869;
  color: #121212;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin-right: 1rem;
}

.author-info h4 {
  margin: 0;
  color: #ffffff;
  font-size: 1.1rem;
}

.author-info p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

.cta-section {
  position: relative;
  z-index: 1;
  background-color: rgba(30, 30, 35, 0.7);
  border-top: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  padding: 5rem 2rem;
  margin-top: 3rem;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.cta-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, #e4b869, #f0d398);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cta-section p {
  font-size: 1.2rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
}

@media (max-width: 992px) {
  .hero-section {
    flex-direction: column;
    text-align: center;
    padding: 3rem 1.5rem;
  }

  .hero-content {
    max-width: 100%;
    margin-bottom: 3rem;
  }

  h1 {
    font-size: 2.8rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .cta-buttons {
    justify-content: center;
  }

  .hero-image {
    flex: 0 0 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .testimonials-container {
    flex-direction: column;
    align-items: center;
  }

  .testimonial-card {
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  h1 {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .features-section h2,
  .testimonials-section h2,
  .cta-section h2 {
    font-size: 2rem;
  }
}
</style>
