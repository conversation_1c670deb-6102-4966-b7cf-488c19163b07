import { fileURLToPath, URL } from "node:url";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vuetify from "vite-plugin-vuetify";
import Pages from "vite-plugin-pages";
import sitemap from "vite-plugin-sitemap";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vuetify({ autoImport: true }),
    Pages({
      dirs: "src/pages",
      extensions: ["vue"],
    }),
    sitemap({
      hostname: "https://veo3.ai", // 替换为实际域名
      exclude: [
        '/404', 
        '/[...all]',
        '/history' // 如果历史记录页面是用户私有的
      ],
      dynamicRoutes: [
        '/about' // 显式添加不会被自动扫描的页面
      ]
    }),
  ],
  ssr: {
    noExternal: ["vuetify"],
  },
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
});
