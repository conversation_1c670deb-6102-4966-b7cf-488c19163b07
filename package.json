{"name": "veo3-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite-ssg build", "preview": "vite preview"}, "dependencies": {"@mdi/font": "^7.4.47", "jwt-decode": "^4.0.0", "pinia": "^3.0.3", "vite-plugin-pages": "^0.33.0", "vite-plugin-sitemap": "^0.8.2", "vite-plugin-vuetify": "^2.1.1", "vite-ssg": "^27.0.1", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuetify": "^3.8.8"}, "devDependencies": {"@types/node": "^22.15.30", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}